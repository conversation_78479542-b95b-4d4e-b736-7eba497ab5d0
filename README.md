# Augment续杯 浏览器插件

这是一个Chrome浏览器扩展，用于在Augment登录页面添加"续杯"功能。

> **重要提示：** 使用前请在扩展设置中输入您自己的域名邮箱后缀（如 example.com），扩展不再提供默认域名。未设置域名将无法使用续杯功能！

## 功能

- 在Augment登录页面添加"续杯"按钮
- 点击按钮后自动生成随机邮箱地址
- 自动填入登录表单并点击继续按钮
- 支持自定义邮箱后缀
- 支持自定义随机字符串位数（默认为12位）

## 开发指南

### 环境要求

- Node.js (推荐 v14 或更高版本)
- npm (推荐 v6 或更高版本)

### 安装依赖

```bash
npm install
```

### 构建扩展

```bash
npm run build
```

构建完成后，将在 `dist` 目录中生成一个 ZIP 文件，可以直接分享给其他人使用。

## 安装方法

### 开发版本

1. 克隆此仓库
2. 安装依赖: `npm install`
3. 构建扩展: `npm run build`
4. 打开Chrome浏览器，进入扩展管理页面 (chrome://extensions/)
5. 开启"开发者模式"
6. 点击"加载已解压的扩展程序"
7. 选择 `dist/build` 目录

### 用户版本

1. 下载最新的 ZIP 文件
2. 解压 ZIP 文件
3. 打开Chrome浏览器，进入扩展管理页面 (chrome://extensions/)
4. 开启"开发者模式"
5. 点击"加载已解压的扩展程序"
6. 选择解压后的文件夹

## 使用方法

1. 安装扩展后，点击扩展图标，**必须设置您自己的邮箱后缀**（如 example.com）和随机字符串位数（默认为12位）
2. 访问 [Augment登录页面](https://login.augmentcode.com/)
3. 在登录页面上，您将看到原始"Continue"按钮下方出现一个"续杯"按钮
4. 点击"续杯"按钮，它将自动生成随机邮箱并填入表单，然后自动点击继续按钮

> **注意：** 如果您没有设置邮箱后缀，点击"续杯"按钮时会收到提示，无法继续操作。

## 无限续杯原理说明

Augment续杯插件利用了Augment登录系统的特性，通过自动生成随机邮箱地址来实现"无限续杯"功能。以下是其工作原理：

### 基本原理

1. **随机邮箱生成**：插件会根据用户设置的位数（默认12位）生成随机字符串，并与用户提供的域名后缀组合，形成一个全新的邮箱地址。

2. **自动填充**：生成的随机邮箱会自动填入Augment登录页面的邮箱输入框中。

3. **自动提交**：插件会自动点击页面上的"Continue"按钮，提交这个新生成的邮箱。

4. **绕过验证**：由于Augment登录系统不会立即验证邮箱的真实性，只要邮箱格式正确，系统就会接受并创建一个新的会话。

### 技术实现

- **域名控制**：用户需要使用自己控制的域名作为邮箱后缀，这样即使Augment系统发送验证邮件，也不会对实际使用造成影响。

- **随机性保证**：通过生成足够长度的随机字符串（默认12位），确保每次生成的邮箱地址都是唯一的，避免与已注册账户冲突。

- **DOM操作**：插件通过JavaScript操作页面DOM元素，实现自动填充和点击功能，无需用户手动操作。

### 使用建议

- 建议使用自己真实拥有的域名，这样可以完全控制该域名下的邮箱。
- 适当调整随机字符串位数，既保证唯一性，又不至于过长。
- 请合理使用此功能，遵守相关服务条款和法律法规。

## 项目结构

```
augment-refill/
├── dist/               # 构建输出目录
├── scripts/            # 构建脚本
├── src/                # 源代码
│   ├── background.js   # 后台脚本
│   ├── content.js      # 内容脚本
│   ├── icon.ico        # 扩展图标
│   ├── manifest.json   # 扩展配置
│   ├── popup.html      # 弹出窗口HTML
│   └── popup.js        # 弹出窗口脚本
├── package.json        # 项目配置
└── README.md           # 项目说明
```
